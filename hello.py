#!/usr/bin/env python
"""
extract_fremdleistungsreport.py
--------------------------------
Loop through every *.xls / *.xlsx file in the same folder as this script
and append the rows from the “Fremdleistungsreport” sub-table to a single
CSV called `fremdleistungsreport_combined.csv`.

Run once in a clean directory:

    uv pip install pandas openpyxl xlrd   # installs deps quickly with uv
    python extract_fremdleistungsreport.py
"""

import pandas as pd
from pathlib import Path
import re

# ----------------------------------------------------------------------
# tweak these if your layout differs
MARKER          = "Fremdleistungsreport"            # section headline
STOP_WORDS      = {"Material", "Material APs"}      # first-column values that end the table
OUTFILE         = "fremdleistungsreport_combined.csv"
COLS_EXPECTED   = ["Leistungsnummer", "Bezeichnung",
                   "Menge", "Einheit", "Gewerk"]
# ----------------------------------------------------------------------


def extract_table(df: pd.DataFrame) -> pd.DataFrame | None:
    """Return Fremdleistungsreport table or None if not found."""
    # 1) locate section header
    header_row = df.index[df.iloc[:, 0].astype(str).str.contains(
        fr"^{MARKER}$", case=False, na=False)].tolist()
    if not header_row:
        return None
    header_row = header_row[0]

    # 2) actual column names are on the next row
    table_start = header_row + 1
    cols_raw = df.iloc[table_start].fillna("").astype(str).str.strip()
    # map expected names → actual positions
    col_map = {name: idx for idx, name in cols_raw.items()
               if name in COLS_EXPECTED}
    if len(col_map) < 5:
        return None  # layout too different

    # 3) collect data until stop condition
    data = []
    for r in range(table_start + 1, len(df)):
        first_cell = str(df.iat[r, 0]).strip()
        if first_cell == "" or first_cell in STOP_WORDS:
            break
        row = {name: df.iat[r, col_map[name]] for name in COLS_EXPECTED}
        # skip completely blank rows
        if all(pd.isna(v) or str(v).strip() == "" for v in row.values()):
            continue
        data.append(row)

    if not data:
        return None
    return pd.DataFrame(data)


def main() -> None:
    records = []
    for f in Path(".").glob("*.xls*"):
        try:
            df = pd.read_excel(f, header=None, dtype=str)  # raw read
            sub = extract_table(df)
            if sub is not None:
                sub["Quelle"] = f.name                      # keep provenance
                records.append(sub)
        except Exception as exc:  # noqa: BLE001
            print(f"⚠️  Skipping {f}: {exc}")

    if not records:
        print("Inga Fremdleistungsreport-tabeller hittades.")
        return

    out = pd.concat(records, ignore_index=True)
    # clean up numeric column
    out["Menge"] = pd.to_numeric(out["Menge"], errors="coerce")
    out.to_csv(OUTFILE, index=False, encoding="utf-8")
    print(f"✅  Sparade {len(out)} rader till {OUTFILE}")


if __name__ == "__main__":
    main()